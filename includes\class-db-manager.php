<?php
if (!defined('ABSPATH')) {
    exit;
}

class DAB_DB_Manager {

    public static function install() {
        self::create_base_tables();
    }

    public static function create_base_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        $tables_table = $wpdb->prefix . 'dab_tables';
        $fields_table = $wpdb->prefix . 'dab_fields';
        $forms_table = $wpdb->prefix . 'dab_forms';
        $relationships_table = $wpdb->prefix . 'dab_relationships';
        $views_table = $wpdb->prefix . 'dab_views';
        $students_table = $wpdb->prefix . 'dab_students';

        // Check if we need to add field_order column
        $field_order_exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM information_schema.columns
                WHERE table_schema = %s
                AND table_name = %s
                AND column_name = 'field_order'",
                DB_NAME,
                $fields_table
            )
        );

        $sql_tables = "CREATE TABLE IF NOT EXISTS $tables_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            table_label VARCHAR(255) NOT NULL,
            table_slug VARCHAR(255) NOT NULL UNIQUE,
            description TEXT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        $sql_fields = "CREATE TABLE IF NOT EXISTS $fields_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            table_id BIGINT(20) UNSIGNED NOT NULL,
            field_label VARCHAR(255) NOT NULL,
            field_slug VARCHAR(255) NOT NULL,
            field_type VARCHAR(50) NOT NULL,
            required TINYINT(1) DEFAULT 0,
            placeholder VARCHAR(255) NULL,
            options TEXT NULL,
            field_order INT DEFAULT 0,
            formula_expression TEXT NULL,
            lookup_table_id BIGINT(20) NULL,
            lookup_display_column VARCHAR(255) NULL,
            rollup_related_table BIGINT(20) NULL,
            rollup_foreign_key VARCHAR(255) NULL,
            rollup_aggregation_type VARCHAR(50) NULL,
            rollup_target_field VARCHAR(255) NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        $sql_forms = "CREATE TABLE IF NOT EXISTS $forms_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            form_name VARCHAR(255) NOT NULL,
            table_id BIGINT(20) UNSIGNED NOT NULL,
            fields LONGTEXT NULL,
            conditional_logic LONGTEXT NULL,
            notify_email VARCHAR(255) NULL,
            notify_message TEXT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        $sql_relationships = "CREATE TABLE IF NOT EXISTS $relationships_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            table_a_id BIGINT(20) UNSIGNED NOT NULL,
            field_a_slug VARCHAR(255) NOT NULL,
            table_b_id BIGINT(20) UNSIGNED NOT NULL,
            display_column VARCHAR(255) DEFAULT NULL,
            relationship_type VARCHAR(50) DEFAULT 'one_to_many',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        $sql_views = "CREATE TABLE IF NOT EXISTS $views_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            view_name VARCHAR(255) NOT NULL,
            table_id BIGINT(20) UNSIGNED NOT NULL,
            selected_fields LONGTEXT NULL,
            filter_conditions LONGTEXT NULL,
            sort_order VARCHAR(255) NULL,
            is_public TINYINT(1) DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        $sql_students = "CREATE TABLE IF NOT EXISTS $students_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT(20) DEFAULT NULL,
            approval_status VARCHAR(20) DEFAULT 'Pending',
            approval_notes TEXT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NULL,
            PRIMARY KEY (id)
        ) $charset_collate;";

        dbDelta($sql_tables);
        dbDelta($sql_fields);
        dbDelta($sql_forms);
        dbDelta($sql_relationships);
        dbDelta($sql_views);
        dbDelta($sql_students);

        self::ensure_advanced_field_columns_exist();
        self::ensure_column_exists($forms_table, 'conditional_logic', 'LONGTEXT NULL');
        self::ensure_column_exists($forms_table, 'notify_email', 'VARCHAR(255) NULL');
        self::ensure_column_exists($forms_table, 'notify_message', 'TEXT NULL');
        self::ensure_column_exists($views_table, 'sort_order', 'VARCHAR(255) NULL');
        self::ensure_column_exists($views_table, 'is_public', 'TINYINT(1) DEFAULT 0');

        // Add field_order column if it doesn't exist
        if (!$field_order_exists) {
            self::ensure_column_exists($fields_table, 'field_order', 'INT DEFAULT 0');

            // Initialize field_order values based on id to maintain existing order
            $wpdb->query("UPDATE $fields_table SET field_order = id");
        }

        // New approval workflow tables
        $approval_levels_table = $wpdb->prefix . 'dab_approval_levels';
        $approval_history_table = $wpdb->prefix . 'dab_approval_history';

        $sql_approval_levels = "CREATE TABLE IF NOT EXISTS $approval_levels_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            table_id BIGINT(20) UNSIGNED NOT NULL,
            level_name VARCHAR(255) NOT NULL,
            level_order INT NOT NULL,
            approver_roles TEXT NULL,
            notification_email VARCHAR(255) NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        $sql_approval_history = "CREATE TABLE IF NOT EXISTS $approval_history_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            record_id BIGINT(20) UNSIGNED NOT NULL,
            table_id BIGINT(20) UNSIGNED NOT NULL,
            level_id BIGINT(20) UNSIGNED NOT NULL,
            status VARCHAR(20) NOT NULL,
            notes TEXT NULL,
            user_id BIGINT(20) UNSIGNED NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        dbDelta($sql_approval_levels);
        dbDelta($sql_approval_history);

        // Add current_approval_level column to data tables
        self::ensure_column_exists_in_all_tables('current_approval_level', 'BIGINT(20) DEFAULT 0');
    }

    public static function create_data_table($table_slug) {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();
        $table_name = $wpdb->prefix . 'dab_' . sanitize_title($table_slug);

        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT(20) DEFAULT NULL,
            approval_status VARCHAR(20) DEFAULT 'Pending',
            approval_notes TEXT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NULL,
            PRIMARY KEY (id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Get the database column type for a field type
     *
     * @param string $field_type Field type
     * @return string SQL column type
     */
    public static function get_column_type_for_field($field_type) {
        switch ($field_type) {
            case 'number':
                return 'DECIMAL(10,2)';
            case 'checkbox':
                return 'TINYINT(1)';
            case 'date':
                return 'DATE';
            case 'relationship':
                return 'BIGINT(20)';
            case 'autoincrement':
                return 'VARCHAR(255)';
            case 'textarea':
            case 'json':
                return 'LONGTEXT';
            case 'signature':
            case 'media':
            case 'conditional_logic':
            case 'social_media':
            case 'multiselect':
            case 'enhanced_currency':
                return 'LONGTEXT';
            default:
                return 'TEXT';
        }
    }

    /**
     * Create default fields for a table based on its type/name
     *
     * @param int $table_id Table ID
     * @param string $table_slug Table slug
     * @return array Array of created field IDs
     */
    public static function create_default_fields($table_id, $table_slug) {
        global $wpdb;
        $fields_table = $wpdb->prefix . 'dab_fields';
        $data_table_name = $wpdb->prefix . 'dab_' . sanitize_title($table_slug);

        // Check if table already has fields
        $existing_fields = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $fields_table WHERE table_id = %d",
            $table_id
        ));

        if ($existing_fields > 0) {
            // Table already has fields, no need to create defaults
            return array();
        }

        // Define default fields based on table name
        $default_fields = array();
        $field_ids = array();

        // Common fields for all tables
        $default_fields[] = array(
            'field_label' => 'Name',
            'field_slug' => 'name',
            'field_type' => 'text',
            'required' => 1,
            'placeholder' => 'Enter name',
            'field_order' => 1
        );

        $default_fields[] = array(
            'field_label' => 'Description',
            'field_slug' => 'description',
            'field_type' => 'textarea',
            'required' => 0,
            'placeholder' => 'Enter description',
            'field_order' => 2
        );

        // Add specific fields based on table slug
        if (strpos($table_slug, 'product') !== false) {
            // Product-specific fields
            $default_fields[] = array(
                'field_label' => 'Price',
                'field_slug' => 'price',
                'field_type' => 'number',
                'required' => 1,
                'placeholder' => 'Enter price',
                'field_order' => 3
            );

            $default_fields[] = array(
                'field_label' => 'Quantity',
                'field_slug' => 'quantity',
                'field_type' => 'number',
                'required' => 1,
                'placeholder' => 'Enter quantity',
                'field_order' => 4
            );
        } elseif (strpos($table_slug, 'user') !== false || strpos($table_slug, 'customer') !== false) {
            // User/Customer-specific fields
            $default_fields[] = array(
                'field_label' => 'Email',
                'field_slug' => 'email',
                'field_type' => 'email',
                'required' => 1,
                'placeholder' => 'Enter email',
                'field_order' => 3
            );

            $default_fields[] = array(
                'field_label' => 'Phone',
                'field_slug' => 'phone',
                'field_type' => 'text',
                'required' => 0,
                'placeholder' => 'Enter phone number',
                'field_order' => 4
            );
        } elseif (strpos($table_slug, 'order') !== false) {
            // Order-specific fields
            $default_fields[] = array(
                'field_label' => 'Order Date',
                'field_slug' => 'order_date',
                'field_type' => 'date',
                'required' => 1,
                'placeholder' => 'Select order date',
                'field_order' => 3
            );

            $default_fields[] = array(
                'field_label' => 'Total Amount',
                'field_slug' => 'total_amount',
                'field_type' => 'number',
                'required' => 1,
                'placeholder' => 'Enter total amount',
                'field_order' => 4
            );
        }

        // Create fields and add columns to data table
        foreach ($default_fields as $field_data) {
            // Add table_id to field data
            $field_data['table_id'] = $table_id;

            // Insert field
            $wpdb->insert($fields_table, $field_data);
            $field_id = $wpdb->insert_id;

            if ($field_id) {
                $field_ids[] = $field_id;

                // Add column to data table
                $column_type = self::get_column_type_for_field($field_data['field_type']);
                self::ensure_column_exists($data_table_name, $field_data['field_slug'], $column_type);
            }
        }

        return $field_ids;
    }

    public static function add_column_to_table($table_name, $field_slug, $field_type) {
        global $wpdb;

        $column_type = 'TEXT';
        switch ($field_type) {
            case 'number': $column_type = 'BIGINT(20)'; break;
            case 'email':
            case 'text':
            case 'radio':
            case 'select':
            case 'checkbox':
            case 'lookup':
            case 'autoincrement': $column_type = 'VARCHAR(255)'; break;
            case 'textarea': $column_type = 'TEXT'; break;
            case 'date': $column_type = 'DATE'; break;
            case 'currency': $column_type = 'DECIMAL(20,2)'; break;
            case 'boolean': $column_type = 'TINYINT(1)'; break;
            case 'json': $column_type = 'LONGTEXT'; break;
            case 'rating': $column_type = 'TINYINT(1)'; break;
            case 'formula':
            case 'rollup': $column_type = 'DECIMAL(20,2)'; break;
            case 'signature':
            case 'media':
            case 'conditional_logic':
            case 'social_media':
            case 'multiselect':
            case 'enhanced_currency': $column_type = 'LONGTEXT'; break;
        }

        $column_exists = $wpdb->get_results("SHOW COLUMNS FROM `$table_name` LIKE '$field_slug'");
        if (empty($column_exists)) {
            $wpdb->query("ALTER TABLE `$table_name` ADD `$field_slug` $column_type NULL");
        }
    }

    /**
     * Ensure all field columns exist in data tables
     */
    public static function ensure_all_field_columns_exist() {
        global $wpdb;

        $tables_table = $wpdb->prefix . 'dab_tables';
        $fields_table = $wpdb->prefix . 'dab_fields';

        $tables = $wpdb->get_results("SELECT * FROM $tables_table");

        foreach ($tables as $table) {
            $data_table_name = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);

            // Check if table exists
            if ($wpdb->get_var("SHOW TABLES LIKE '$data_table_name'") != $data_table_name) {
                self::create_data_table($table->table_slug);
            }

            // Get fields for this table
            $fields = $wpdb->get_results($wpdb->prepare(
                "SELECT * FROM $fields_table WHERE table_id = %d",
                $table->id
            ));

            foreach ($fields as $field) {
                // Skip inline_table fields as they don't need columns
                if ($field->field_type === 'inline_table') {
                    continue;
                }

                $column_type = self::get_column_type_for_field($field->field_type);
                self::ensure_column_exists($data_table_name, $field->field_slug, $column_type);
            }

            // Ensure approval workflow columns exist
            self::ensure_column_exists($data_table_name, 'current_approval_level', 'BIGINT(20) DEFAULT 0');
        }
    }

    public static function ensure_advanced_field_columns_exist() {
        global $wpdb;
        $fields_table = $wpdb->prefix . 'dab_fields';

        $required_columns = [
            'lookup_table_id' => 'BIGINT(20) NULL',
            'lookup_display_column' => 'VARCHAR(255) NULL',
            'rollup_related_table' => 'BIGINT(20) NULL',
            'rollup_foreign_key' => 'VARCHAR(255) NULL',
            'rollup_aggregation_type' => 'VARCHAR(50) NULL',
            'rollup_target_field' => 'VARCHAR(255) NULL',
            'filtered_table_id' => 'BIGINT(20) NULL',
            'filtered_display_column' => 'VARCHAR(255) NULL',
            'filtered_filter_field' => 'VARCHAR(255) NULL',
            'filtered_filter_value' => 'VARCHAR(255) NULL',
            'field_options' => 'LONGTEXT NULL', // Ensure field_options column exists for template compatibility
        ];

        foreach ($required_columns as $column => $definition) {
            $exists = $wpdb->get_results($wpdb->prepare(
                "SHOW COLUMNS FROM `$fields_table` LIKE %s", $column
            ));
            if (empty($exists)) {
                $wpdb->query("ALTER TABLE `$fields_table` ADD `$column` $definition");
            }
        }
    }

    public static function ensure_column_exists($table, $column, $definition) {
        global $wpdb;
        $exists = $wpdb->get_results($wpdb->prepare("SHOW COLUMNS FROM `$table` LIKE %s", $column));
        if (empty($exists)) {
            $wpdb->query("ALTER TABLE `$table` ADD COLUMN `$column` $definition");
        }
    }

    public static function ensure_column_exists_in_all_tables($column_name, $column_definition) {
        global $wpdb;
        $tables_table = $wpdb->prefix . 'dab_tables';
        $tables = $wpdb->get_results("SELECT * FROM $tables_table");

        foreach ($tables as $table) {
            $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);
            self::ensure_column_exists($data_table, $column_name, $column_definition);
        }
    }

    // This method has been replaced by the public version above
}
